import 'package:deewan/app/data/models/identity_model.dart';
import 'package:deewan/app/data/models/media_model.dart';
import 'package:deewan/core/enums/enums.dart';
import 'package:isar_community/isar.dart';
import 'package:deewan/app/data/models/message_models.dart';

part 'room_model.g.dart';

@collection
class Room {
  Id id = Isar.autoIncrement;
  @Index(unique: true, replace: true)
  late String roomId;

  /// Room type discriminator (public, private, order)
  @enumerated
  late RoomType type;

  /// One-to-one link to room type, only one of the three should be set
  final IsarLink<PrivateRoom> privateData = IsarLink<PrivateRoom>();
  final IsarLink<PublicRoom> publicData = IsarLink<PublicRoom>();
  final IsarLink<OrderRoom> orderData = IsarLink<OrderRoom>();
  final  imageUrl = IsarLink<ImageUrl>();
  @Backlink(to: 'room')
  final IsarLinks<Message> messages = IsarLinks<Message>();
  /// participants
  @Backlink(to: 'room')
  final IsarLinks<RoomMembership>? participants = IsarLinks();
  @Backlink(to: 'room')
  final IsarLinks<Participant>? roomMemberships = IsarLinks();

  /// property specific for the this user in this room
  // late List<Message> pinnedMessages = [];
  RoomStatus? status;
  DateTime? lastMessageTimestamp;
  String? lastMessageContent;
   String? lastMessageAuthor;
   DeliveryStatus? lastMessageDeliveryStatus;
   StarType? lastMessageStarType;
   int? unreadMessagesCount;

  Room({
    required this.roomId,
    this.status,
    this.lastMessageTimestamp,
    this.lastMessageContent,
    this.lastMessageAuthor,
    this.lastMessageDeliveryStatus,
    this.lastMessageStarType,
    this.unreadMessagesCount,
  });
}

@collection
class PrivateRoom {
  Id id = Isar.autoIncrement;

  /// Link back to base room
  final IsarLink<Room> room = IsarLink<Room>();
  // Private room–specific data could go here
}

@collection
class PublicRoom {
  Id id = Isar.autoIncrement;
  final IsarLink<Room> room = IsarLink<Room>();
   String? title; // channel name
   String? description;
  PublicRoom({this.title, this.description});
}

@collection
class OrderRoom {
  Id id = Isar.autoIncrement;
  final IsarLink<Room> room = IsarLink<Room>();
   String orderTitle;
  @Enumerated(EnumType.name)
   OrderCategory orderCategory;
  @Enumerated(EnumType.name)
   OrderStatus orderStatus;
   DateTime orderDate;

  OrderRoom({
    required this.orderTitle,
    required this.orderCategory,
    required this.orderDate,
    required this.orderStatus,
  });
}

@embedded
class Participant {
  Id id = Isar.autoIncrement;
  final room = IsarLink<Room>();
  final identity = IsarLink<Identity>();
  @Enumerated(EnumType.name)
  ParticipantRole? role; // owner, admin, member, guest
  DateTime? joinedAt;
  DateTime? leftAt;
  bool? isBanned;
  Participant();
}

@collection
class RoomMembership {
  Id id = Isar.autoIncrement;
  final room = IsarLink<Room>();
  final identity = IsarLink<Identity>(); // local identity only

  // Sync state
  String? lastReadMessageId;
  DateTime? lastReadAt;
  int unreadCount = 0;

  // Local preferences
  bool isMuted = false;
  bool isPinned = false;
  bool isArchived = false;
  String? customTitle;
  String? draftText;
  RoomMembership();
}
