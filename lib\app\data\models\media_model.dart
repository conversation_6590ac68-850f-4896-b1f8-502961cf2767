import 'package:deewan/app/data/models/address_model.dart';
import 'package:deewan/app/data/models/identity_model.dart';
import 'package:deewan/app/data/models/message_models.dart';
import 'package:deewan/app/data/models/item_models.dart';
import 'package:isar_community/isar.dart';

part 'media_model.g.dart';

@collection
class ImageUrl {
  Id id = Isar.autoIncrement;
  final String? imageUrl;
  final DateTime? createdAt;
  
  @enumerated
  late ImageType imageType;

  @Backlink(to: 'images')
  final identity = IsarLinks<Identity>();

  @Backlink(to: 'images')
  final items = IsarLinks<Item>();

  @Backlink(to: 'images')
  final messages = IsarLinks<Message>();

  @Backlink(to: 'images')
  final addresses = IsarLinks<Address>();

  ImageUrl({
    this.imageUrl,
    this.createdAt, 
    required this.imageType,
  });
}

enum ImageType {
  identityImage,
  itemImage,
  msgImage,
  adressImage,
  linkPreview,
  unknown, // For any unsupported or unrecognized type
}


@collection
class VideoUrl {
  Id id = Isar.autoIncrement;
  final String? videoUrl;
  final String? videoType;
  final DateTime? createdAt;
  VideoUrl(
    this.videoUrl,
    this.videoType,
    this.createdAt,
  );
}

@collection
class AudioUrl {
  Id id = Isar.autoIncrement;
  final String? audioUrl;
  final String? audioType;
  final DateTime? createdAt;
  AudioUrl(
    this.audioUrl,
    this.audioType,
    this.createdAt,
  );
}

@collection
class FileUrl {
  Id id = Isar.autoIncrement;
  final String? fileUrl;
  final String? fileType;
  final DateTime? createdAt;
  FileUrl(
    this.fileUrl,
    this.fileType,
    this.createdAt,
  );
}

@collection
class LinkPreview {
  Id id = Isar.autoIncrement;
  final String? linkUrl;
  final String? linkTitle;
  final String? linkDescription;
  final String? linkImage;
  LinkPreview(
    this.linkUrl,
    this.linkTitle,
    this.linkDescription,
    this.linkImage,
  );
}
