import 'package:deewan/app/data/models/identity_model.dart';
import 'package:isar_community/isar.dart';

part 'contact_page_model.g.dart';

@collection
class ContactCard {
  Id id = Isar.autoIncrement;

  @Index(unique: true, caseSensitive: false, replace: true)
  late String serverId; // global UUID/ID for this contact card

  // Basic contact information
  String? name;
  String? phoneNumber;
  String? email;
  String? notes;

  // Owner of this contact card
  final ownerIdentity = IsarLink<Identity>();
  // The contact this card represents
  final contactIdentity = IsarLink<Identity>();

  // Timestamps
  late DateTime createdAt;
  DateTime? updatedAt;

  ContactCard({
    required this.serverId,
    this.name,
    this.phoneNumber,
    this.email,
    this.notes,
  });
}

