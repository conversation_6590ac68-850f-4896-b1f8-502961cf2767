import 'package:deewan/app/data/models/identity_model.dart';
import 'package:deewan/app/data/models/media_model.dart';
import 'package:isar_community/isar.dart';

part 'user_profile_model.g.dart';

@collection
class UserProfile {
  Id id = Isar.autoIncrement;

  @Index(unique: true, caseSensitive: false)
   String serverId; // global UUID/ID for this user profile

  // Basic profile information
  String? title;
  String? email;
  String? phone;
  String? pinCode;

  // Verification status
  bool isVerified = false;// device regestration not personality
  bool emailVerified = false;
  bool phoneVerified = false;

  // Profile avatar
  final avatar = IsarLink<ImageUrl>();

  // Relationship to identities owned by this user profile
  @Backlink(to: 'serverId')
  final myIdentitiesId = IsarLinks<Identity>();

  // Timestamps
  DateTime? createdAt;
  DateTime? updatedAt;
  DateTime? lastActiveAt;

  UserProfile({
    required this.serverId,
    this.title,
    this.email,
    this.phone,
    this.pinCode,
    this.isVerified = false,
    this.emailVerified = false,
    this.phoneVerified = false,
    this.createdAt,
    this.updatedAt,
    this.lastActiveAt,
  }) ;
}
