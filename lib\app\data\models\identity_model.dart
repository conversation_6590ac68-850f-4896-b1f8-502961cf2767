import 'package:deewan/app/data/models/address_model.dart';
import 'package:deewan/app/data/models/room_model.dart';
import 'package:deewan/app/data/models/store_page_model.dart';
import 'package:deewan/app/data/models/user_profile_model.dart';
import 'package:deewan/app/data/models/contact_page_model.dart';
import 'package:deewan/app/data/models/media_model.dart';
import 'package:deewan/core/enums/enums.dart';
import 'package:isar_community/isar.dart';

part 'identity_model.g.dart';

@collection
class Identity {
  Id id = Isar.autoIncrement;
  
  @Index(unique: true, caseSensitive: false, replace: true)
   String serverId; // canonical UUID from server
   String displayName;
  @Index(unique: true, caseSensitive: false, replace: true)
  String? username;
  String? phoneNumber;
  String? email;
  DateTime? lastSeen;
  bool isVerified = false;
  @Enumerated(EnumType.name)
   IdentityCategory category ; // private, store, bot
  @Enumerated(EnumType.name)
   IdentityStatus status;
  final images = IsarLinks<ImageUrl>();
  final IsarLink<ContactCard> contactPage = IsarLink<ContactCard>();
  final IsarLink<StorePage> storePage = IsarLink<StorePage>();
  
  Identity({
    required this.serverId,
    required this.displayName,
    this.username,
    this.phoneNumber,
    this.email,
    required this.status,
    required this.category,
  });
}

// My local identities (owned by UserProfile)
@collection
class MyIdentity {
  Id id = Isar.autoIncrement;
  
  @Index(unique: true, caseSensitive: false, replace: true)
  late String serverId; // same as Identity.serverId when interacting
  
  // Links to UserProfile (private, never shared)
  final userProfile = IsarLink<UserProfile>();
  
  // Identity data (mirrors Identity fields)
  late String displayName;
  String? username;
  String? phoneNumber;
  String? email;
  DateTime? lastSeen;
  bool isVerified = false;
  
  @Enumerated(EnumType.name)
  late IdentityCategory category;
  
  @Enumerated(EnumType.name)
  late IdentityStatus status;
  
  // Local-only fields
  bool isDefault = false;
  DateTime? createdAt;
  
  final images = IsarLinks<ImageUrl>();
  final IsarLink<ContactCard>? contactPage = IsarLink<ContactCard>();
  final IsarLink<StorePage>? storePage = IsarLink<StorePage>();
  
  MyIdentity({
    required this.serverId,
    required this.displayName,
    this.username,
    this.phoneNumber,
    this.email,
    required this.status,
    required this.category,
    this.isDefault = false,
  });
}

@collection
class Contact {
  Id id = Isar.autoIncrement;
  final identity = IsarLink<Identity>(); // always remote Identity
  final owner = IsarLink<MyIdentity>(); // always MyIdentity
  
  String? nickname;
  bool isBlocked = false;
  bool isFavorite = false;
  DateTime? lastInteractionAt;
  
  Contact();
}

