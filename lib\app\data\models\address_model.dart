import 'package:deewan/app/data/models/item_models.dart';
import 'package:deewan/app/data/models/media_model.dart';
import 'package:isar_community/isar.dart';

part 'address_model.g.dart';

@collection
class Address {
  Id id = Isar.autoIncrement;

  final String? building; // building name, house number, etc.
  final String? floor;
  final String? appartment; // flat, room, appartment numbers, etc.
  final String? street;
  final String? city;
  final String? state;
  final String? zip;
  final String? country;
  final String? addressId;
  final double? addressLatitude;
  final double? addressLongitude;
  final DateTime? addressCreatedAt;
  final DateTime? addressUpdatedAt;
  final String? addressLabel;
  final String? addressStatus; // active, inactive, deleted ,
  final images = IsarLinks<ImageUrl>();
  final String? addressType; // home, work, school, etc.

  Address({
    this.building,
    this.floor,
    this.appartment,
    this.addressType,
    this.addressId,
    this.addressLabel,
    this.addressLatitude,
    this.addressLongitude,
    this.addressCreatedAt,
    this.addressUpdatedAt,
    this.addressStatus,
    this.street,
    this.city,
    this.state,
    this.zip,
    this.country,
  });
}
