import 'package:deewan/app/data/models/cart_model.dart';
import 'package:deewan/app/data/models/collection_model.dart';
import 'package:deewan/app/data/models/identity_model.dart';
import 'package:deewan/core/enums/enums.dart';
import 'package:isar_community/isar.dart';

part 'store_page_model.g.dart';

@collection
class StorePage {
  Id id = Isar.autoIncrement;

  @Index(unique: true, caseSensitive: false ,replace: true)
  late String serverId; // global UUID/ID for this store page

  // Store information
  String? name;
  String? description;
  String? workingHours;
  String? address;
  String? phoneNumber;
  String? email;
  String? website;

  // Store status
  bool isActive = true;
  bool isVerified = false;
  late String? verificationSerialCode;
  // One-to-one relationship with Identity (store identities only)
  final identity = IsarLink<Identity>();

  // Store-specific collections
  @Backlink(to: 'storePage')
  final collections = IsarLinks<CollectionModel>();

  // Store participants/managers
  @Backlink(to: 'storeIdentity')
  final participants = IsarLinks<StoreParticipant>();

  // Template cart for customers to clone
  //final templateCart = IsarLinks<Cart>();

  // All carts associated with this store (customer carts + template)
  @Backlink(to: 'store')
  final IsarLinks<Cart>? associatedCarts = IsarLinks<Cart>();

  // Timestamps
  late DateTime createdAt;
  late DateTime? updatedAt;
  late DateTime? lastActiveAt;

  StorePage({
    required this.serverId,
    this.name,
    this.description,
    this.workingHours,
    this.address,
    this.phoneNumber,
    this.email,
    this.website,
    this.isActive = true,
    this.isVerified = false,
    DateTime? createdAt,
  });
}

@collection
class StoreParticipant {
  Id id = Isar.autoIncrement;

  // Relationships
  final storeIdentity = IsarLink<Identity>(); // Store identity
  final participantIdentity =
      IsarLink<Identity>(); // Private identity managing the store

  // Role and permissions
  @enumerated
  late StoreRole role;
  late bool? isActive;

  // Timestamps
  late DateTime joinedAt;
  late DateTime? lastActiveAt;

  // Optional: fast-unique guard if you want enforcement
  @Index(unique: true, caseSensitive: false)
  String pairKey = ""; // set to "${storeServerId}::${participantServerId}"

  StoreParticipant({
    required this.role,
    this.isActive,
    this.lastActiveAt,
    this.pairKey = "",
  }) ;
}

