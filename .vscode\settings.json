{"geminicodeassist.inlineSuggestions.enableAuto": true, "java.autobuild.enabled": true, "java.import.gradle.enabled": true, "java.configuration.updateBuildConfiguration": "automatic", "java.jdt.ls.java.home": "C:\\Users\\<USER>\\workplace\\jdk-21.0.7", "dart.flutterSdkPath": ".fvm/versions/3.35.3", "search.exclude": {"**/.fvm": true}, "files.watcherExclude": {"**/.fvm": true}, "cmake.ignoreCMakeListsMissing": true}