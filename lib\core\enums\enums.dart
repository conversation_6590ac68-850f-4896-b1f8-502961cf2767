// App configuration enums
enum AppLanguage { english, arabic, french, spanish }

enum AppMode { dev, prod }

enum AppOrientation { portrait, landscape }

enum AppThemeMode { light, dark, system }

// Status and state enums
enum StatusRequest {
  none,
  loading,
  success,
  failure,
  serverFailure,
  offlineFailure,
}

enum RoomType { private, public, order }

enum ParticipantRole { customer, staff, admin, subscriber }

enum Permissions { sendMessage, moderate, deleteMessage, fulfillOrder }

enum ConnectionStatus {
  connected,
  disconnected,
  connecting,
  reconnecting,
  failed,
}

enum AuthStatus {
  unknown,
  authenticated,
  unauthenticated,
  loading,
  error,
  expired,
}

enum RoomStatus { active, inactive }

enum PresenceStatus { online, offline, away, busy, invisible }

enum NetworkStatus { online, offline, limited, unknown }

enum NetworkQuality { excellent, good, fair, poor }

// File and media enums
enum FileType { image, video, audio, document, pdf, any, other }

enum ImageFormat { jpeg, png, webp, gif, bmp }

enum AudioFormat { mp3, aac, wav, flac, ogg }

enum VideoFormat { mp4, avi, mov, wmv, flv, webm }

// Sync and processing enums
enum SyncStatus { idle, syncing, completed, failed, paused }

enum QueueStatus { idle, processing, paused, error }

enum UploadStatus { idle, uploading, success, failed, cancelled }

enum DownloadStatus { idle, downloading, success, failed, cancelled }

// Permission enums
enum Permission {
  camera,
  microphone,
  storage,
  location,
  contacts,
  notifications,
}

enum PermissionResult { granted, denied, restricted, permanentlyDenied }

enum PermissionStatus { granted, denied, restricted, permanentlyDenied }

// Search and content enums
enum SearchType { message, room, contact, media, all }

enum SortOrder { ascending, descending }

enum SortBy { name, date, popularity, rating, price, size }

enum FilterType { category, price, rating, date, location, type }

// Media playback enums
enum RecordingState { idle, recording, paused, stopped }

enum AudioPlaybackState { idle, playing, paused, stopped, loading, error }

// User and profile enums
enum UserRole { admin, user, moderator, guest }

enum Gender { male, female, other, preferNotToSay }

enum MaritalStatus { single, married, divorced, widowed, other }

enum EducationLevel {
  none,
  primary,
  secondary,
  bachelor,
  master,
  doctorate,
  other,
}

enum EmploymentStatus {
  employed,
  unemployed,
  selfEmployed,
  student,
  retired,
  other,
}

// Notification enums
enum NotificationType { info, success, warning, error }

enum PriorityLevel { low, medium, high, urgent }

// Task and workflow enums
enum TaskStatus { pending, inProgress, completed, cancelled, failed }

enum PaymentStatus {
  pending,
  processing,
  completed,
  failed,
  cancelled,
  refunded,
}

enum OrderCategory {
  food,
  cart,
  health,
  taxi,
  delivery,
  appointment,
  event,
  feedback,
  enquiry,
  verification,
}

enum OrderStatus {
  pending,
  confirmed,
  accomplished,
  processing,
  onGoing,
  delivered,
  cancelled,
  returned,
}

enum DeliveryStatus { sent, delivered, read, failed, cancelled }

// Backup and sync enums
enum BackupFrequency { daily, weekly, monthly, never }

enum ContactSyncStatus { idle, syncing, completed, failed }

// Legacy compatibility
enum ThemeMode { light, dark, system }

enum StarType {
  like,
  dislike,
  heart,
  smile,
  laugh,
  angry,
} //the type of star/reaction given to the message

enum PollType {
  singleChoice,
  multipleChoice,

  rating,
} //the type of poll created for the message

enum Messagetype {
  text,
  reply,
  forward,
  image,
  video,
  audio,
  file,
  location,
  sticker,
  reaction,
  poll,
  contacts, //contacts card to show contact details of person/business as a card in chat
  gif,
  link,
  calendar,
  appointment, // time and date and location and target account or location or
  productsCart, // on click it opens a page that contains products to be deliverd /sell/ buy...
  healthCareElement, // healthcare profile elements : lap , med ,prosedure ,ddx reports , xr, AI reports that contains spt sympt....
  deal, //(ongoing to join or an empty template deal(modified to specific use) ) information
  currency,
  package,

  unknown, // Added "unknown" type
  // Added "custom" type
  custom, // Added "custom" type
}

// Identity and Profile enums
enum IdentityStatus { active, inactive, blocked, incognito }

enum IdentityCategory { private, store, bot }

// Store-related enums
enum StoreRole { owner, manager, staff, viewer }

// Contact and Permission enums
enum ContactPermissionLevel { view, contact, blocked }

enum TrustLevel { unknown, low, medium, high, verified }
